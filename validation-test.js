// 题目数据验证功能测试

// 模拟验证函数
function validateQuestionData(questionData, row, questionType) {
  const errors = [];
  
  // 验证题目问题
  if (!questionData.content || questionData.content.trim() === "") {
    errors.push("题目问题不能为空");
  }
  
  // 验证题目难度
  if (!questionData.difficulty || questionData.difficulty.trim() === "") {
    errors.push("题目难度不能为空");
  }
  
  // 验证所属题库
  if (!questionData.gameCategory || questionData.gameCategory.trim() === "") {
    errors.push("所属题库不能为空");
  }
  
  // 验证题目类型
  if (!questionData.questionType || questionData.questionType.trim() === "") {
    errors.push("题目类型不能为空");
  }
  
  // 验证正确答案
  if (!questionData.correctAnswer || questionData.correctAnswer.trim() === "") {
    errors.push("正确答案不能为空");
  }
  
  // 如果是选择题，验证选项
  if (questionType === "选择题") {
    const optionsText = row["选项"];
    if (!optionsText || optionsText.trim() === "") {
      errors.push("选择题的选项不能为空");
    } else {
      // 检查选项是否有内容
      const lines = optionsText.split("\n").filter((line) => line.trim());
      const validOptions = lines.filter((line) => {
        const trimmedLine = line.trim();
        if (trimmedLine) {
          const cleanOption = trimmedLine.replace(/^[A-Z][:：]\s*/, "");
          const finalOption = cleanOption.replace(/【答案】/g, "").trim();
          return finalOption !== "";
        }
        return false;
      });
      
      if (validOptions.length < 2) {
        errors.push("选择题至少需要2个有效选项");
      }
    }
  }
  
  return errors;
}

// 测试用例
const testCases = [
  {
    name: "正常的选择题数据",
    questionData: {
      content: "1+1等于几？",
      difficulty: "简单",
      gameCategory: "数学题库",
      questionType: "选择题",
      correctAnswer: "2"
    },
    row: {
      "选项": "A: 1\nB: 2\nC: 3\nD: 4"
    },
    questionType: "选择题",
    expectedErrors: []
  },
  {
    name: "题目问题为空",
    questionData: {
      content: "",
      difficulty: "简单",
      gameCategory: "数学题库",
      questionType: "选择题",
      correctAnswer: "2"
    },
    row: {
      "选项": "A: 1\nB: 2"
    },
    questionType: "选择题",
    expectedErrors: ["题目问题不能为空"]
  },
  {
    name: "题目难度为空",
    questionData: {
      content: "2+2等于几？",
      difficulty: "",
      gameCategory: "数学题库",
      questionType: "选择题",
      correctAnswer: "4"
    },
    row: {
      "选项": "A: 3\nB: 4"
    },
    questionType: "选择题",
    expectedErrors: ["题目难度不能为空"]
  },
  {
    name: "所属题库为空",
    questionData: {
      content: "3+3等于几？",
      difficulty: "简单",
      gameCategory: "",
      questionType: "选择题",
      correctAnswer: "6"
    },
    row: {
      "选项": "A: 5\nB: 6"
    },
    questionType: "选择题",
    expectedErrors: ["所属题库不能为空"]
  },
  {
    name: "正确答案为空",
    questionData: {
      content: "4+4等于几？",
      difficulty: "简单",
      gameCategory: "数学题库",
      questionType: "选择题",
      correctAnswer: ""
    },
    row: {
      "选项": "A: 7\nB: 8"
    },
    questionType: "选择题",
    expectedErrors: ["正确答案不能为空"]
  },
  {
    name: "选择题选项为空",
    questionData: {
      content: "5+5等于几？",
      difficulty: "简单",
      gameCategory: "数学题库",
      questionType: "选择题",
      correctAnswer: "10"
    },
    row: {
      "选项": ""
    },
    questionType: "选择题",
    expectedErrors: ["选择题的选项不能为空"]
  },
  {
    name: "选择题选项不足",
    questionData: {
      content: "6+6等于几？",
      difficulty: "简单",
      gameCategory: "数学题库",
      questionType: "选择题",
      correctAnswer: "12"
    },
    row: {
      "选项": "A: 12"
    },
    questionType: "选择题",
    expectedErrors: ["选择题至少需要2个有效选项"]
  },
  {
    name: "多个错误",
    questionData: {
      content: "",
      difficulty: "",
      gameCategory: "数学题库",
      questionType: "选择题",
      correctAnswer: "8"
    },
    row: {
      "选项": ""
    },
    questionType: "选择题",
    expectedErrors: ["题目问题不能为空", "题目难度不能为空", "选择题的选项不能为空"]
  }
];

// 运行测试
console.log("开始运行验证功能测试...\n");

testCases.forEach((testCase, index) => {
  console.log(`测试 ${index + 1}: ${testCase.name}`);
  
  const actualErrors = validateQuestionData(
    testCase.questionData,
    testCase.row,
    testCase.questionType
  );
  
  const passed = JSON.stringify(actualErrors.sort()) === JSON.stringify(testCase.expectedErrors.sort());
  
  if (passed) {
    console.log("✅ 通过");
  } else {
    console.log("❌ 失败");
    console.log("期望错误:", testCase.expectedErrors);
    console.log("实际错误:", actualErrors);
  }
  
  console.log("");
});

console.log("测试完成！");
