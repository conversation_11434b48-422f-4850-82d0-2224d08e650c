<template>
  <div class="clearingContent">
    <div class="headerContent">
      <img
        @click="goBack"
        src="../assets/cancel.png"
        alt="返回"
        class="back-btn"
      />
      <span>{{ answerPageName }}</span>
    </div>

    <div class="resultContainer">
      <div class="resultTitle">闯关结果</div>

      <div class="scoreCircle">
        <div class="scoreText">
          <div class="scoreNumber">{{ correctCount }}/{{ totalCount }}</div>
          <div class="scoreLabel">答对题数</div>
        </div>
      </div>

      <div class="encourageText" :style="{ color: encourageColor }">
        {{ encourageMessage }}
      </div>

      <div class="scoreDetails">
        <div class="detailItem">
          <span class="detailLabel">正确率：</span>
          <span class="detailValue">{{ successRate }}%</span>
        </div>
        <!-- <div class="detailItem">
          <span class="detailLabel">用时：</span>
          <span class="detailValue">{{ formatTime }}</span>
        </div> -->
      </div>

      <div class="actionButtons">
        <div @click="goNextLevel" class="actionBtn nextBtn btnStyleAm">
          <span>返回关卡</span>
        </div>
        <!-- <div @click="retryLevel" class="actionBtn retryBtn btnStyleAm">
          <span>重新挑战</span>
        </div>
        <div @click="goHome" class="actionBtn homeBtn btnStyleAm">
          <span>返回首页</span>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";

const route = useRoute();
const router = useRouter();

let answerPageName = ref("");
let correctCount = ref(0);
let totalCount = ref(0);
let startTime = ref(Date.now());
let groupNumber=ref(0)
// 计算正确率
const successRate = computed(() => {
  if (totalCount.value === 0) return 0;
  return Math.round((correctCount.value / totalCount.value) * 100);
});

// 格式化用时
const formatTime = computed(() => {
  const seconds = Math.floor((Date.now() - startTime.value) / 1000);
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}分${remainingSeconds}秒`;
});

// 鼓励语和颜色
const encourageMessage = computed(() => {
  const rate = successRate.value;
  if (rate === 100) return "完美通关！太棒了！";
  if (rate >= 80) return "表现优秀！继续加油！";
  if (rate >= 60) return "不错的成绩！再接再厉！";
  if (rate >= 40) return "还有进步空间，加油！";
  return "别灰心，多练习就会进步！";
});

const encourageColor = computed(() => {
  const rate = successRate.value;
  if (rate >= 80) return "#2fbc08";
  if (rate >= 60) return "#ff741c";
  return "#f5a131";
});

function goBack() {
  router.push({ path: "/content", query: { name: answerPageName.value } });
}

function goNextLevel() {
  // 返回到内容选择页面，可以选择下一关
  router.push({ path: "/content", query: { name: answerPageName.value } });
}

function retryLevel() {
  // 重新开始当前关卡
  router.back();
}

function goHome() {
  router.push({ path: "/" });
}

onMounted(() => {
  answerPageName.value = route.query.name || "";
  correctCount.value = parseInt(route.query.correctCount) || 0;
  totalCount.value = parseInt(route.query.totalCount) || 0;
  groupNumber.value=route.query.groupNumber
  // 记录本关通关历史
  let localList = localStorage.getItem(answerPageName.value+'clearing');
  if (localList) {
    try {
      let arr = JSON.parse(localList);
      if (!Array.isArray(arr)) arr = [];
      // 记录本次通关信息
      arr.push({
        correctCount: correctCount.value,
        totalCount: totalCount.value,
        successRate: successRate.value,
        groupNumber:groupNumber.value
      });
      localStorage.setItem(answerPageName.value+'clearing', JSON.stringify(arr));
    } catch (e) {
      // 解析失败则重置
      let arr = [{
        correctCount: correctCount.value,
        totalCount: totalCount.value,
        successRate: successRate.value,
        groupNumber:groupNumber.value
      }];
      localStorage.setItem(answerPageName.value+'clearing', JSON.stringify(arr));
    }
  } else {
    let arr = [{
      correctCount: correctCount.value,
      totalCount: totalCount.value,
      successRate: successRate.value,
      groupNumber:groupNumber.value
    }];
    localStorage.setItem(answerPageName.value+'clearing', JSON.stringify(arr));
  }
  // localStorage.getItem(answerPageName)
});
</script>

<style scoped>
.clearingContent {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-sizing: border-box;
}

.headerContent {
  display: flex;
  align-items: center;
  padding: 20px 20px 10px 20px;
  font-size: 20px;
  font-weight: bold;
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.back-btn {
  width: 20px;
  height: 20px;
  margin-right: 15px;
  cursor: pointer;
  filter: brightness(0) invert(1);
}

.resultContainer {
  padding: 40px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.resultTitle {
  font-size: 32px;
  font-weight: bold;
  color: #fff;
  margin-bottom: 40px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.scoreCircle {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background: linear-gradient(45deg, #fff, #f0f0f0);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.scoreText {
  text-align: center;
}

.scoreNumber {
  font-size: 48px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.scoreLabel {
  font-size: 16px;
  color: #666;
}

.encourageText {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 30px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.scoreDetails {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 40px;
  backdrop-filter: blur(10px);
}

.detailItem {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  color: #fff;
  font-size: 18px;
}

.detailItem:last-child {
  margin-bottom: 0;
}

.detailLabel {
  font-weight: normal;
}

.detailValue {
  font-weight: bold;
}

.actionButtons {
  display: flex;
  flex-direction: column;
  gap: 15px;
  width: 100%;
  max-width: 300px;
}

.actionBtn {
  width: 100%;
  height: 55px;
  border-radius: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: bold;
  color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nextBtn {
  background: linear-gradient(60deg, #2fbc08 0%, #48d466 100%);
}

.retryBtn {
  background: linear-gradient(60deg, #ff741c 0%, #f5a131 100%);
}

.homeBtn {
  background: linear-gradient(60deg, #2d8cf0 0%, #5fd3f0 100%);
}

.btnStyleAm:active {
  transform: perspective(1500px) rotateX(8deg) translateY(-4px);
  box-shadow: 0 18px 30px -8px rgba(0, 0, 0, 0.35);
}
</style>
