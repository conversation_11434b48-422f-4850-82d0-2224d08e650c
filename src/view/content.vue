<template>
  <div class="content-container">
    <div class="headerContent">
      <img
        @click="goBack"
        src="../assets/cancel.png"
        alt="返回"
        class="back-btn"
      />
      <span>{{ answerPageName }}</span>
    </div>
    <div class="group-list">
      <div
        v-for="item in groupList"
        :key="item.id"
        class="group-item"
        @click="!getClearFlag(item) && goPrview(item)"
        :class="{ disabled: getClearFlag(item) }"
      >
        <div
          class="level-icon"
          :style="{ background: difficultyMap[item.difficulty].color }"
        >
          <img style="width: 26px; height: 26px" :src="getIcon(item)" />
        </div>
        <span style="margin-left: 10px">{{ item.name }}</span>
        <div
          style="
            display: flex;
            position: absolute;
            right: 20px;
            align-items: center;
          "
          v-if="getClearFlag(item)"
        >
          <div
            style="
              font-weight: normal;
              color: #63e71c;
              margin-right: 10px;
              font-size: 16px;
            "
          >
            <span style="margin-right: 10px"
              >通过率：{{ getClearDetail(item).correctCount }}/{{
                getClearDetail(item).totalCount
              }}
            </span>
          </div>
          <!-- <div style="color: #63e71c">已完成</div> -->
        </div>
      </div>
    </div>
    <div v-if="isStart" class="startGo">开始</div>
    <TittopText ref="tittopTextRef" />
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { generateAuthHeaders } from "../utils/utils";
import { useRouter, useRoute } from "vue-router";
import axios from "axios";
import { getGameLevels } from "../api/answer";
import TittopText from "../components/modal/tittopText.vue";
const route = useRoute();
const router = useRouter();
const difficultyMap = {
  简单: { name: "简单", color: "#2ecc71", icon: "Sunny", index: 1 },
  中等: { name: "中等", color: "#f39c12", icon: "Cloudy", index: 2 },
  困难: { name: "困难", color: "#e74c3c", icon: "Lightning", index: 3 },
};
const icons = import.meta.glob("../assets/*.png", {
  eager: true,
  import: "default",
});
const getIcon = (level) => {
  // 假设difficulty为数字，图片命名为 1.png、2.png 等
  const path = `../assets/${difficultyMap[level.difficulty].index}.png`;
  return icons[path] || "";
};
let isStart = ref(false);
let answerPageName = ref("");
let groupList = ref([]);
const tittopTextRef = ref(null);
function goPrview(item) {
  if (item.questions.length == 0) {
    tittopTextRef.value?.open("该关卡暂无试题");
    return;
  }
  isStart.value = true;
  setTimeout(() => {
    router.push({
      path: "/page",
      query: {
        name: route.query.name,
        groupNumber: item.levelNumber,
        questions: JSON.stringify(item.questions),
      },
    });
  }, 500);
}

function goBack() {
  router.push({ path: "/" });
}

let clearingList = ref([]);
/**
 * 判断某一关卡是否已通关
 * @param {Object} item - 关卡对象，需包含 levelNumber
 * @returns {Object|undefined} 返回通关记录对象，未通关则返回 undefined
 */
function getClearFlag(item) {
  if (!item || !item.levelNumber) return undefined;
  const record = clearingList.value.find(
    (j) => j.groupNumber == item.levelNumber
  );
  return clearingList.value.find((j) => j.groupNumber == item.levelNumber);
}
function getClearDetail(item) {
  if (!item || !item.levelNumber) return undefined;
  const record = clearingList.value.find(
    (j) => j.groupNumber == item.levelNumber
  );
  return record;
}
onMounted(async () => {
  answerPageName.value = route.query.name || "";
  let clearIng = localStorage.getItem(route.query.name + "clearing");
  if (clearIng) {
    clearingList.value = JSON.parse(clearIng);
  }
  if (route.query.isIndex) {
    let localList = localStorage.getItem(route.query.name);
    let arr = JSON.parse(localList);
    arr.forEach((item) => {
      item.difficulty = item.difficultyName;
    });
    try {
      // const res = await axios.post(
      //   `https://api2.kkzhw.com/mall-portal/openapi/quiz/game/${route.query.name}/levels`,
      //   { gameCategory: route.query.name, settingList: arr },
      //   {
      //     headers: await generateAuthHeaders(),
      //   }
      // );
      getGameLevels(route.query.name, {
        gameCategory: route.query.name,
        settingList: arr,
      }).then((res) => {
        if (res.code === 200 && Array.isArray(res.data.levels)) {
          groupList.value = res.data.levels;
          localStorage.setItem("groupList", JSON.stringify(res.data.levels));
        }
      });
    } catch (e) {
      console.error("获取分组失败", e);
    }
  } else {
    let groupData = localStorage.getItem("groupList");
    groupList.value = JSON.parse(groupData);
  }
});
</script>

<style scoped>
.content-container {
  width: 100%;
  min-height: 100vh;
  background: #f7f7f7;
  box-sizing: border-box;
  /* padding-bottom: 40px; */
  overflow: hidden;
}
.headerContent {
  display: flex;
  align-items: center;
  padding: 20px 20px 10px 20px;
  font-size: 20px;
  font-weight: bold;
  background: #fff;
  border-bottom: 1px solid #eee;
}
.back-btn {
  width: 20px;
  height: 20px;
  margin-right: 12px;
  cursor: pointer;
}
.group-list {
  height: calc(100vh - 60px);
  overflow: auto;
  padding: 20px;
  box-sizing: border-box;
  /* 隐藏滚动条，兼容所有主流浏览器 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE和Edge */
}
.group-list::-webkit-scrollbar {
  display: none; /* Chrome、Safari 和 Opera */
}
.group-item {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 8px;
  margin-bottom: 16px;
  padding: 16px 20px;
  font-size: 18px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s;
  position: relative;
}
.group-item:hover {
  background: #e6f7ff;
}
.group-img {
  width: 46px;
  height: 46px;
  margin-right: 14px;
  border-radius: 6px;
  object-fit: cover;
}
.startGo {
  position: fixed;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100vh;
  background: rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 120px;
  font-weight: 1000;
  color: #fdc438;
  text-shadow: 2px 2px 0 #ebb941, 4px 4px 0 #e2b036,
    0 1px 8px rgba(0, 0, 0, 0.25);
  z-index: 999;
}
.level-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}
.disabled {
  opacity: 0.9; /* 降低透明度 */
  cursor: not-allowed !important; /* 鼠标显示禁用图标 */
}
</style>
