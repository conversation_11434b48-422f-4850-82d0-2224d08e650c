<template>
  <!-- 模板部分保持不变 -->
  <div class="excel-viewer">
    <div class="controls">
      <div class="file-upload-wrapper">
        <input
          type="file"
          id="fileInput"
          @change="handleFile"
          accept=".xlsx,.xls"
          class="file-input-hidden"
        />
        <label for="fileInput" class="file-upload-btn">
          <svg
            class="upload-icon"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
          >
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
            <polyline points="7,10 12,15 17,10"></polyline>
            <line x1="12" y1="15" x2="12" y2="3"></line>
          </svg>
          {{ file ? file.name : "选择Excel文件" }}
        </label>
      </div>

      <button
        class="parse-btn"
        @click="parseExcel"
        :disabled="!file || loading"
        :class="{ loading: loading }"
      >
        <svg v-if="loading" class="btn-spinner" viewBox="0 0 24 24">
          <circle
            cx="12"
            cy="12"
            r="10"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
          ></circle>
          <path
            d="M12 2 A10 10 0 0 1 22 12"
            stroke="currentColor"
            stroke-width="2"
            fill="none"
            stroke-linecap="round"
          ></path>
        </svg>
        <svg
          v-else
          class="parse-icon"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
        >
          <path
            d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"
          ></path>
          <polyline points="14,2 14,8 20,8"></polyline>
          <line x1="16" y1="13" x2="8" y2="13"></line>
          <line x1="16" y1="17" x2="8" y2="17"></line>
          <polyline points="10,9 9,9 8,9"></polyline>
        </svg>
        {{ loading ? "解析中..." : "解析Excel" }}
      </button>
    </div>

    <!-- Loading 指示器 -->
    <div v-if="loading" class="loading-indicator">
      <div class="loading-spinner"></div>
      <span>正在解析Excel文件，请稍候...</span>
    </div>

    <div v-if="error" class="error">{{ error }}</div>

    <!-- 验证错误表格 -->
    <div v-if="validationErrors.length > 0" class="validation-errors">
      <h3 style="color: red">数据验证失败，以下数据不合规：</h3>
      <el-table
        :data="validationErrors"
        style="width: 100%; margin-bottom: 20px"
        border
        stripe
      >
        <el-table-column
          prop="rowIndex"
          label="行号"
          width="80"
          align="center"
        />
        <el-table-column
          prop="sheetName"
          label="工作表"
          width="120"
          align="center"
        />
        <el-table-column
          prop="content"
          label="题目问题"
          width="200"
          align="center"
        />
        <el-table-column prop="errors" label="错误信息" align="center">
          <template #default="scope">
            <div style="color: red">
              <div v-for="errorMsg in scope.row.errors" :key="errorMsg">
                {{ errorMsg }}
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 正常数据表格 -->
    <el-table
      v-if="parsedData.length > 0"
      ref="categoryTable"
      :data="parsedData"
      :resizable="false"
      style="width: 100%"
      border
    >
      <el-table-column prop="content" label="题目问题" align="center" />
      <el-table-column prop="image" label="配图" width="120" align="center">
        <template #default="scope">
          <div v-if="scope.row.imageUrl">
            <img
              :src="scope.row.imageUrl"
              style="max-width: 100px; max-height: 80px"
            />
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="difficulty"
        label="题目难度"
        width="90"
        align="center"
      />
      <el-table-column
        prop="gameCategory"
        label="所属题库"
        width="100"
        align="center"
      />
      <el-table-column
        prop="questionType"
        label="题目类型"
        width="90"
        align="center"
      />
      <el-table-column
        prop="correctAnswer"
        label="正确答案"
        width="200"
        align="center"
      />
    </el-table>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import ExcelJS from "exceljs";
import { stsTokenApi } from "@/api/answer.js";

const file = ref(null);
const loading = ref(false);
const error = ref(null);
const sheets = ref([]);
const activeSheet = ref("");
const parsedData = ref([]); // 存储转换后的数组格式数据
const validationErrors = ref([]); // 存储验证错误的数据

const activeSheetData = computed(() => {
  return sheets.value.find((sheet) => sheet.name === activeSheet.value);
});

function isImageCell(cell) {
  return cell?.type === "image" || cell?.formula?.includes("DISPIMG");
}

const handleFile = (e) => {
  file.value = e.target.files[0];
  error.value = null;
  // 每次选择新文件时清空之前的数据
  sheets.value = [];
  parsedData.value = [];
  validationErrors.value = []; // 清空验证错误
};

// 清空文件输入的函数
const clearFileInput = () => {
  file.value = null;
  // 同时清空HTML input元素的值
  const fileInput = document.getElementById("fileInput");
  if (fileInput) {
    fileInput.value = "";
  }
};

// ✅ 数据预验证函数 - 在上传OSS之前验证数据
const preValidateExcelData = async (workbook) => {
  const errors = [];
  const parsedSheets = [];

  // 处理每个工作表进行预验证
  workbook.eachSheet((worksheet) => {
    console.log(`🔍 预验证工作表: ${worksheet.name}`);

    const sheetData = {
      name: worksheet.name,
      headers: [],
      jsonData: [],
    };

    // 提取表头
    const headerRow = worksheet.getRow(1);
    if (headerRow?.values) {
      sheetData.headers = headerRow.values.slice(1);
    }

    const jsonRows = [];

    // 处理数据行进行验证
    worksheet.eachRow({ includeEmpty: true }, (row, rowIndex) => {
      if (rowIndex === 1) return; // 跳过标题行

      const jsonRowData = {};
      let hasContent = false;

      // 检查这一行是否为空行
      row.eachCell({ includeEmpty: true }, (cell) => {
        if (cell.value || cell.formula || cell.text) {
          hasContent = true;
        }
      });

      // 如果整行都是空的，跳过这一行
      if (!hasContent) {
        return;
      }

      // 提取行数据
      row.eachCell({ includeEmpty: true }, (cell, colIndex) => {
        const headerName = sheetData.headers[colIndex - 1] || `列${colIndex}`;

        // 对于图片单元格，暂时标记为图片类型
        if (cell.formula?.includes("DISPIMG")) {
          jsonRowData[headerName] = { type: "image", placeholder: true };
        } else {
          const cellValue = cell.value || cell.text || null;
          jsonRowData[headerName] = cellValue;
        }
      });

      jsonRows.push(jsonRowData);
    });

    sheetData.jsonData = jsonRows;
    parsedSheets.push(sheetData);
  });

  // 进行数据验证
  parsedSheets.forEach((sheet) => {
    const questionType = sheet.name;

    sheet.jsonData.forEach((row, rowIndex) => {
      const questionData = {
        gameCategory: row["题库名称"] || row["题库"] || "",
        content: row["问题"] || "",
        difficulty: row["题目难度"] || "",
        questionType: questionType,
        correctAnswer: row["正确答案"] || "",
        answerAnalysis: row["答案解析"] || "",
      };

      // 验证必填字段
      const validationErrors = validateQuestionData(
        questionData,
        row,
        questionType
      );

      if (validationErrors.length > 0) {
        errors.push({
          rowIndex: rowIndex + 2, // +2 因为第一行是表头，数组索引从0开始
          sheetName: sheet.name,
          content: questionData.content || "(空)",
          errors: validationErrors,
        });
      }
    });
  });

  return {
    hasErrors: errors.length > 0,
    errors: errors,
    parsedSheets: parsedSheets,
  };
};

const parseExcel = async () => {
  if (!file.value) {
    error.value = "请先选择文件";
    return;
  }

  loading.value = true;
  error.value = null;
  sheets.value = [];
  validationErrors.value = []; // 清空验证错误

  try {
    const arrayBuffer = await readFileAsArrayBuffer(file.value);

    // ✅ 使用两种方法：ExcelJS 和 JSZip 直接解析
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.load(arrayBuffer);

    // ✅ 首先进行数据预验证，不上传图片
    console.log("🔍 开始数据预验证...");
    const preValidationResult = await preValidateExcelData(workbook);

    if (preValidationResult.hasErrors) {
      // 如果有验证错误，直接显示错误，不上传OSS
      validationErrors.value = preValidationResult.errors;
      error.value = `数据验证失败，共发现 ${preValidationResult.errors.length} 条不合规数据，请修正后重新上传`;
      // 验证失败后也清空文件输入
      clearFileInput();
      return;
    }

    // ✅ 验证通过后，开始处理图片上传
    console.log("📸 数据验证通过，开始处理图片上传...");

    // 存储所有图片数据，按顺序排列
    const allImages = [];

    // ✅ 图片缓存映射，用于处理重复图片
    const imageCache = new Map(); // key: 图片哈希, value: { url, fileName, uploadResult }

    // ✅ 上传图片到OSS并获取URL
    console.log(`📸 开始处理 ${workbook.media.length} 张图片...`);

    for (let index = 0; index < workbook.media.length; index++) {
      const media = workbook.media[index];
      try {
        const fileName =
          media.name || `excel_image_${Date.now()}_${index + 1}.png`;

        // ✅ 生成图片哈希值，用于检测重复图片
        const imageHash = generateImageHash(media.buffer);

        let uploadResult;
        let isFromCache = false;

        // ✅ 检查缓存中是否已有相同图片
        if (imageCache.has(imageHash)) {
          uploadResult = imageCache.get(imageHash);
          isFromCache = true;
          console.log(
            `🔄 图片 ${index + 1} 使用缓存: ${fileName} -> ${uploadResult.url}`
          );
        } else {
          // ✅ 新图片，需要上传到OSS
          const fileObject = arrayBufferToFile(
            media.buffer,
            fileName,
            media.type
          );

          console.log(`📤 正在上传图片 ${index + 1}: ${fileName}`);

          // 上传到OSS并获取URL
          uploadResult = await uploadImageToOSS(fileObject, fileName);

          // ✅ 如果上传成功，添加到缓存
          if (uploadResult && uploadResult.success && uploadResult.url) {
            imageCache.set(imageHash, {
              url: uploadResult.url,
              fileName: fileName,
              uploadResult: uploadResult,
            });
          }
        }

        if (uploadResult && uploadResult.url) {
          allImages.push({
            index: index,
            data: uploadResult.url, // 使用OSS返回的URL
            ossUrl: uploadResult.url,
            fileName: fileName,
            type: media.type,
            uploadSuccess: true,
            uploadResult: uploadResult,
            isFromCache: isFromCache,
            imageHash: imageHash,
          });

          if (isFromCache) {
            console.log(
              `✅ 图片 ${index + 1} 使用缓存成功: ${uploadResult.url}`
            );
          } else {
            console.log(`✅ 图片 ${index + 1} 上传成功: ${uploadResult.url}`);
          }
        } else {
          // 如果上传失败，使用base64作为备选
          const base64 = arrayBufferToBase64(media.buffer);
          const dataUrl = `data:${media.type};base64,${base64}`;

          allImages.push({
            index: index,
            data: dataUrl,
            fileName: fileName,
            type: media.type,
            uploadSuccess: false,
            error: "OSS上传失败，使用base64显示",
          });
          console.log(`❌ 图片 ${index + 1} 上传失败，使用base64显示`);
        }
      } catch (e) {
        console.error(`❌ 图片 ${index + 1} 处理失败:`, e);
        // 失败时使用base64作为备选
        try {
          const base64 = arrayBufferToBase64(media.buffer);
          const dataUrl = `data:${media.type};base64,${base64}`;
          allImages.push({
            index: index,
            data: dataUrl,
            fileName: `image_${index + 1}.png`,
            type: media.type,
            uploadSuccess: false,
            error: e.message,
          });
        } catch (fallbackError) {
          console.error(`❌ 图片 ${index + 1} 完全处理失败:`, fallbackError);
        }
      }
    }

    console.log(` 总共找到 ${allImages.length} 张图片`);

    const parsedSheets = [];

    // 处理每个工作表
    workbook.eachSheet((worksheet) => {
      console.log(`📋 处理工作表: ${worksheet.name}`);

      const sheetData = {
        name: worksheet.name,
        headers: [],
        rows: [],
        jsonData: [], // 新增：用于存储JSON格式数据
      };

      // 提取表头
      const headerRow = worksheet.getRow(1);
      if (headerRow?.values) {
        sheetData.headers = headerRow.values.slice(1);
      }

      const rows = [];
      const jsonRows = [];

      // ✅ 创建图片位置映射
      const imagePositionMap = new Map();
      let imageIndex = 0;

      // 获取工作表中的图片位置信息
      const worksheetImages = worksheet.getImages();
      console.log(
        `🖼️  工作表 ${worksheet.name} 中发现 ${worksheetImages.length} 个图片位置`
      );

      // 为每个图片位置分配对应的图片数据
      worksheetImages.forEach((img) => {
        const row = img.range.tl.row + 1; // ExcelJS行号从0开始，转换为1开始
        const col = img.range.tl.col + 1; // ExcelJS列号从0开始，转换为1开始
        const key = `${row}-${col}`;

        if (imageIndex < allImages.length) {
          imagePositionMap.set(key, allImages[imageIndex]);
          console.log(`📍 图片 ${imageIndex + 1} 位置: 行${row}, 列${col}`);
          imageIndex++;
        }
      });

      // 直接处理所有行
      worksheet.eachRow({ includeEmpty: true }, (row, rowIndex) => {
        if (rowIndex === 1) return; // 跳过标题行

        const rowData = [];
        const jsonRowData = {};

        // ✅ 检查这一行是否为空行
        let hasContent = false;
        row.eachCell({ includeEmpty: true }, (cell) => {
          if (cell.value || cell.formula || cell.text) {
            hasContent = true;
          }
        });

        // 如果整行都是空的，跳过这一行
        if (!hasContent) {
          console.log(`⏭️ 跳过空行: 行${rowIndex}`);
          return;
        }

        row.eachCell({ includeEmpty: true }, (cell, colIndex) => {
          const headerName = sheetData.headers[colIndex - 1] || `列${colIndex}`;

          // ✅ 新的解决方案：直接按位置处理图片
          if (cell.formula?.includes("DISPIMG")) {
            console.log(
              `🔍 发现DISPIMG公式: ${cell.formula} 位置: 行${rowIndex}, 列${colIndex}`
            );

            // 检查这个位置是否有图片
            const positionKey = `${rowIndex}-${colIndex}`;
            let imageData = null;
            let imageInfo = null;

            // 首先尝试精确位置匹配
            if (imagePositionMap.has(positionKey)) {
              imageInfo = imagePositionMap.get(positionKey);
              imageData = imageInfo.data;
              console.log(`✅ 精确位置匹配: 行${rowIndex}, 列${colIndex}`);
            } else {
              // 尝试附近位置匹配（允许1格误差）
              for (let r = rowIndex - 1; r <= rowIndex + 1; r++) {
                for (let c = colIndex - 1; c <= colIndex + 1; c++) {
                  const nearKey = `${r}-${c}`;
                  if (imagePositionMap.has(nearKey)) {
                    imageInfo = imagePositionMap.get(nearKey);
                    imageData = imageInfo.data;
                    console.log(
                      `📍 附近位置匹配: 目标(${rowIndex},${colIndex}) -> 实际(${r},${c})`
                    );
                    break;
                  }
                }
                if (imageData) break;
              }
            }

            // 如果还是没找到，按顺序分配
            if (!imageData && allImages.length > 0) {
              // 计算当前是第几个DISPIMG公式
              let dispimgCount = 0;
              for (let r = 2; r <= rowIndex; r++) {
                const tempRow = worksheet.getRow(r);
                tempRow.eachCell(
                  { includeEmpty: true },
                  (tempCell, tempCol) => {
                    if (tempCell.formula?.includes("DISPIMG")) {
                      if (
                        r < rowIndex ||
                        (r === rowIndex && tempCol < colIndex)
                      ) {
                        dispimgCount++;
                      }
                    }
                  }
                );
              }

              if (dispimgCount < allImages.length) {
                imageInfo = allImages[dispimgCount];
                imageData = imageInfo.data;
                console.log(
                  `� 按顺序分配图片: 第${dispimgCount + 1}个DISPIMG -> 图片${
                    dispimgCount + 1
                  }`
                );
              }
            }

            const cellData = {
              type: "image",
              formula: cell.formula,
              imageData: imageData,
              imageInfo: imageInfo,
              position: { row: rowIndex, col: colIndex },
            };

            rowData.push(cellData);
            jsonRowData[headerName] = imageData
              ? {
                  type: "image",
                  data: imageData, // base64用于显示
                  fileName: imageInfo?.name, // 文件名
                  mimeType: imageInfo?.type, // MIME类型
                  fileSize: imageInfo?.buffer?.byteLength, // 文件大小
                  position: { row: rowIndex, col: colIndex },
                  // 注意：file和blob对象不能序列化到JSON中，需要单独处理
                  uploadReady: true, // 标记这个图片可以上传
                }
              : `[图片加载失败: ${cell.formula}]`;

            if (imageData) {
              console.log(`✅ 成功加载图片: 行${rowIndex}, 列${colIndex}`);
            } else {
              console.log(`❌ 图片加载失败: 行${rowIndex}, 列${colIndex}`);
              console.log(`� 可用位置:`, Array.from(imagePositionMap.keys()));
            }
          }
          // ✅ 处理其他可能的图片单元格
          else if (
            cell.value &&
            typeof cell.value === "object" &&
            cell.value.hyperlink
          ) {
            // 处理超链接类型的图片
            rowData.push(cell.value);
            jsonRowData[headerName] = cell.value;
          }
          // 普通文本单元格
          else {
            const cellValue = cell.value || cell.text || null;
            rowData.push(cellValue);
            jsonRowData[headerName] = cellValue;
          }
        });

        rows.push(rowData);
        jsonRows.push(jsonRowData);
      });

      sheetData.rows = rows;
      sheetData.jsonData = jsonRows;
      parsedSheets.push(sheetData);
    });

    sheets.value = parsedSheets;
    if (parsedSheets.length > 0) {
      activeSheet.value = parsedSheets[0].name;
    }

    // 输出JSON格式数据供后端使用
    const finalJsonData = generateBackendJson(parsedSheets);
    parsedData.value = finalJsonData;
    console.log("📊 解析完成，JSON数据:", finalJsonData);

    // 解析成功后清空文件输入
    clearFileInput();
  } catch (err) {
    console.error("❌ 解析失败:", err);
    error.value = `解析失败: ${err.message}`;

    // 解析失败后也清空文件输入
    clearFileInput();
  } finally {
    loading.value = false;
  }
};

// ✅ 生成后端需要的JSON格式数据
function generateBackendJson(parsedSheets) {
  const finalResult = [];
  const errors = [];

  parsedSheets.forEach((sheet) => {
    const questionType = sheet.name; // 判断题、选择题、问答题等

    sheet.jsonData.forEach((row, rowIndex) => {
      const questionData = {
        gameCategory: row["题库名称"] || row["题库"] || "",
        content: row["问题"] || "",
        imageUrl: null,
        difficulty: row["题目难度"] || "",
        questionType: questionType,
        correctAnswer: row["正确答案"] || "",
        answerAnalysis: row["答案解析"] || "",
      };

      // 验证必填字段
      const validationErrors = validateQuestionData(
        questionData,
        row,
        questionType
      );

      if (validationErrors.length > 0) {
        errors.push({
          rowIndex: rowIndex + 2, // +2 因为第一行是表头，数组索引从0开始
          sheetName: sheet.name,
          content: questionData.content || "(空)",
          errors: validationErrors,
        });
        return; // 跳过有错误的数据
      }

      // 处理配图
      if (row["配图"] && typeof row["配图"] === "object" && row["配图"].data) {
        questionData.imageUrl = row["配图"].data;
      }

      // 处理选择题的选项
      if (questionType === "选择题" && row["选项"]) {
        const optionsText = row["选项"];
        const optionsList = [];

        // 按行分割选项
        const lines = optionsText.split("\n").filter((line) => line.trim());

        lines.forEach((line) => {
          const trimmedLine = line.trim();
          if (trimmedLine) {
            // 去掉A:、B:、C:、D:等前缀（支持中英文冒号）
            const cleanOption = trimmedLine.replace(/^[A-Z][:：]\s*/, "");
            // 去掉【答案】标记
            const finalOption = cleanOption.replace(/【答案】/g, "").trim();
            if (finalOption) {
              optionsList.push(finalOption);
            }
          }
        });

        questionData.optionsList = optionsList;

        // 调试输出
        console.log(`选择题选项解析: "${optionsText}" -> `, optionsList);
      }

      finalResult.push(questionData);
    });
  });

  // 注意：这里不再设置验证错误，因为预验证已经处理了
  // 如果执行到这里，说明预验证已经通过，不应该有错误
  return finalResult;
}

// ✅ 验证题目数据
function validateQuestionData(questionData, row, questionType) {
  const errors = [];

  // 验证题目问题
  if (!questionData.content || questionData.content.trim() === "") {
    errors.push("题目问题不能为空");
  }

  // 验证题目难度
  if (!questionData.difficulty || questionData.difficulty.trim() === "") {
    errors.push("题目难度不能为空");
  }

  // 验证所属题库
  if (!questionData.gameCategory || questionData.gameCategory.trim() === "") {
    errors.push("所属题库不能为空");
  }

  // 验证题目类型
  if (!questionData.questionType || questionData.questionType.trim() === "") {
    errors.push("题目类型不能为空");
  }

  // 验证正确答案
  if (!questionData.correctAnswer || questionData.correctAnswer.trim() === "") {
    errors.push("正确答案不能为空");
  }

  // 如果是选择题，验证选项
  if (questionType === "选择题") {
    const optionsText = row["选项"];
    if (!optionsText || optionsText.trim() === "") {
      errors.push("选择题的选项不能为空");
    } else {
      // 检查选项是否有内容
      const lines = optionsText.split("\n").filter((line) => line.trim());
      const validOptions = lines.filter((line) => {
        const trimmedLine = line.trim();
        if (trimmedLine) {
          const cleanOption = trimmedLine.replace(/^[A-Z][:：]\s*/, "");
          const finalOption = cleanOption.replace(/【答案】/g, "").trim();
          return finalOption !== "";
        }
        return false;
      });

      if (validOptions.length < 2) {
        errors.push("选择题至少需要2个有效选项");
      }
    }
  }

  return errors;
}

// ✅ 统计图片数量
function countImages(jsonData) {
  let count = 0;
  jsonData.forEach((row) => {
    Object.values(row).forEach((cell) => {
      if (cell && typeof cell === "object" && cell.type === "image") {
        count++;
      }
    });
  });
  return count;
}

// ✅ 导出JSON数据的方法
const exportJsonData = () => {
  if (parsedData.value.length === 0) {
    error.value = "没有数据可导出";
    return;
  }

  // 创建下载链接
  const dataStr = JSON.stringify(parsedData.value, null, 2);
  const dataBlob = new Blob([dataStr], { type: "application/json" });
  const url = URL.createObjectURL(dataBlob);

  const link = document.createElement("a");
  link.href = url;
  link.download = `questions_data_${new Date().getTime()}.json`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);

  console.log("📁 JSON数据已导出");
};

// 文件处理辅助函数
const readFileAsArrayBuffer = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => resolve(e.target.result);
    reader.onerror = (error) => reject(error);
    reader.readAsArrayBuffer(file);
  });
};

const arrayBufferToBase64 = (buffer) => {
  let binary = "";
  const bytes = new Uint8Array(buffer);
  for (let i = 0; i < bytes.byteLength; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return window.btoa(binary);
};

// ✅ 生成ArrayBuffer的简单哈希值，用于识别重复图片
const generateImageHash = (buffer) => {
  const bytes = new Uint8Array(buffer);
  let hash = 0;

  // 使用简单的哈希算法
  for (let i = 0; i < bytes.length; i++) {
    hash = ((hash << 5) - hash + bytes[i]) & 0xffffffff;
  }

  // 为了更好的唯一性，也考虑文件大小
  return `${hash}_${bytes.length}`;
};

// ✅ 将ArrayBuffer转换为File对象，可以直接上传
const arrayBufferToFile = (buffer, fileName, mimeType) => {
  const blob = new Blob([buffer], { type: mimeType });
  return new File([blob], fileName, { type: mimeType });
};

// ✅ 将ArrayBuffer转换为Blob对象，也可以上传
const arrayBufferToBlob = (buffer, mimeType) => {
  return new Blob([buffer], { type: mimeType });
};

// ✅ 上传图片到OSS的函数 - 参考upload.vue的实现
const uploadImageToOSS = async (fileObject, fileName) => {
  try {
    // 1. 获取STS Token，参考upload.vue的beforeUpload方法
    const response = await stsTokenApi();
    console.log("获取STS Token成功:", response);

    const ext = fileName.split(".").pop();
    const baseFileName = response.data.fileName;

    // 2. 构建OSS上传参数，参考upload.vue的dataObj
    const dataObj = {
      policy: response.data.policy,
      signature: response.data.signature,
      key: response.data.dir + `/${baseFileName}.${ext}`,
      ossaccessKeyId: response.data.accessKeyId,
      dir: response.data.dir,
      host: response.data.host,
      success_action_status: "200",
    };

    console.log("设置的上传参数:", dataObj);

    // 3. 构建FormData，参考upload.vue的customUpload方法
    const formData = new FormData();

    // 添加OSS必需的字段
    Object.keys(dataObj).forEach((key) => {
      if (dataObj[key]) {
        formData.append(key, dataObj[key]);
      }
    });

    // 添加文件，注意file字段要放在最后
    formData.append("file", fileObject);

    console.log("FormData 内容:");
    for (let [key, value] of formData.entries()) {
      console.log(key, value);
    }

    // 4. 发送请求到OSS，参考upload.vue的ossUploadUrl
    const ossUploadUrl = "https://images2.kkzhw.com";
    const uploadResponse = await fetch(ossUploadUrl, {
      method: "POST",
      body: formData,
    });

    if (uploadResponse.ok) {
      // 5. 构建完整的URL，参考upload.vue的handleUploadSuccess
      const fullUrl = `${dataObj.host}/${dataObj.key}`;

      console.log("生成的图片URL:", fullUrl);

      return {
        success: true,
        url: fullUrl,
        fileName: fileName,
        originalResponse: {
          dataObj: dataObj,
          status: uploadResponse.status,
        },
      };
    } else {
      throw new Error(
        `上传失败: ${uploadResponse.status} ${uploadResponse.statusText}`
      );
    }
  } catch (error) {
    console.error("OSS上传失败:", error);
    return {
      success: false,
      error: error.message,
      fileName: fileName,
    };
  }
};

// ✅ 上传图片到服务器的示例函数
const uploadImageToServer = async (imageInfo, uploadUrl) => {
  try {
    const formData = new FormData();

    // 方式1：使用File对象上传
    formData.append("file", imageInfo.file);

    // 方式2：使用Blob对象上传（如果需要自定义文件名）
    // formData.append('file', imageInfo.blob, imageInfo.name);

    // 添加其他参数
    formData.append("fileName", imageInfo.name);
    formData.append("mimeType", imageInfo.type);

    const response = await fetch(uploadUrl, {
      method: "POST",
      body: formData,
    });

    if (response.ok) {
      const result = await response.json();
      console.log("✅ 图片上传成功:", result);
      return result;
    } else {
      throw new Error(`上传失败: ${response.status}`);
    }
  } catch (error) {
    console.error("❌ 图片上传失败:", error);
    throw error;
  }
};

// ✅ 批量上传所有图片的示例函数
const uploadAllImages = async (parsedSheets, uploadUrl) => {
  const uploadResults = [];

  for (const sheet of parsedSheets) {
    for (const row of sheet.rows) {
      for (const cell of row) {
        if (cell.type === "image" && cell.imageInfo) {
          try {
            const result = await uploadImageToServer(cell.imageInfo, uploadUrl);
            uploadResults.push({
              position: cell.position,
              uploadResult: result,
            });
          } catch (error) {
            uploadResults.push({
              position: cell.position,
              error: error.message,
            });
          }
        }
      }
    }
  }

  return uploadResults;
};
</script>

<style scoped>
/* 样式保持不变 */
.cell-image {
  max-width: 120px;
  max-height: 100px;
  display: block;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.image-error {
  color: #e74c3c;
  font-size: 0.9rem;
  padding: 8px;
  background: #ffeaea;
  border-radius: 4px;
  border: 1px solid #f5c6cb;
}

.controls {
  display: flex;
  gap: 20px;
  align-items: center;
  margin-bottom: 30px;
  flex-wrap: wrap;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.export-btn {
  background: #28a745;
}

.export-btn:hover {
  background: #218838;
}

.debug-info {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  border-left: 4px solid #007bff;
}

.debug-info h4 {
  margin: 0 0 10px 0;
  color: #007bff;
  font-size: 1.1rem;
}

.debug-info p {
  margin: 5px 0;
  font-size: 0.9rem;
  color: #6c757d;
}

.json-preview {
  margin-top: 20px;
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.json-preview h4 {
  margin: 0 0 10px 0;
  color: #495057;
  font-size: 1.1rem;
}

.json-preview pre {
  background: #ffffff;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #dee2e6;
  font-size: 0.8rem;
  max-height: 300px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.converted-data-preview {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 2px solid #e9ecef;
}

.converted-data-preview h3 {
  color: #28a745;
  border-bottom-color: #28a745;
}

.excel-viewer {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.error {
  color: #e74c3c;
  background: #fdeded;
  padding: 12px;
  border-radius: 8px;
  margin-top: 15px;
  font-weight: 500;
}

.validation-errors {
  margin: 20px 0;
}

.validation-errors h3 {
  color: #e74c3c !important;
  border-bottom-color: #e74c3c !important;
  margin-bottom: 15px;
}

.sheet-tabs {
  display: flex;
  gap: 10px;
  margin: 20px 0;
  flex-wrap: wrap;
}

.sheet-tabs button {
  padding: 8px 16px;
  background: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.sheet-tabs button.active {
  background: #2196f3;
  color: white;
  border-color: #2196f3;
}

.sheet-content {
  margin-top: 20px;
}

h3 {
  font-size: 1.5rem;
  margin-bottom: 15px;
  color: #2c3e50;
  border-bottom: 2px solid #3498db;
  padding-bottom: 10px;
}

.table-container {
  overflow-x: auto;
  border: 1px solid #eaeaea;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

table {
  width: 100%;
  border-collapse: collapse;
  min-width: 800px;
}

th {
  background: #3498db;
  color: white;
  padding: 12px 15px;
  text-align: left;
  font-weight: 600;
}

td {
  padding: 10px 15px;
  border-bottom: 1px solid #eaeaea;
  vertical-align: middle;
}

tr:nth-child(even) {
  background-color: #f8f9fa;
}

tr:hover {
  background-color: #e3f2fd;
}

/* 文件上传样式 */
.file-upload-wrapper {
  position: relative;
}

.file-input-hidden {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
  overflow: hidden;
}

.file-upload-btn {
  display: inline-flex;
  align-items: center;
  gap: 12px;
  padding: 14px 24px;
  background: rgba(255, 255, 255, 0.95);
  color: #4a5568;
  border: 2px dashed #cbd5e0;
  border-radius: 12px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  min-width: 200px;
  justify-content: center;
}

.file-upload-btn:hover {
  background: rgba(255, 255, 255, 1);
  border-color: #667eea;
  color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
}

.upload-icon {
  width: 20px;
  height: 20px;
  stroke-width: 2;
}

/* 解析按钮样式 */
.parse-btn {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  padding: 14px 28px;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4);
  min-width: 160px;
  justify-content: center;
}

.parse-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(79, 172, 254, 0.6);
}

.parse-btn:disabled {
  background: linear-gradient(135deg, #a0aec0 0%, #cbd5e0 100%);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.parse-btn.loading {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.parse-icon,
.btn-spinner {
  width: 18px;
  height: 18px;
  stroke-width: 2;
}

.btn-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Loading 指示器样式 */
.loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 20px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  margin: 20px 0;
  color: #495057;
  font-weight: 500;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #dee2e6;
  border-top: 2px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>