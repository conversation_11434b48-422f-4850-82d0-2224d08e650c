<template>
  <div>
    <div v-if="!isUploading" class="picUpload_wrap">
      <el-upload
        ref="uploadRef"
        :action="useOss ? ossUploadUrl : minioUploadUrl"
        :data="useOss ? dataObj : {}"
        :http-request="customUpload"
        :multiple="false"
        :on-remove="handleRemove"
        :show-file-list="showFileList"
        :file-list="fileList"
        :before-upload="beforeUpload"
        :on-success="handleUploadSuccess"
        :on-error="handleUploadError"
        :on-preview="handlePreview"
        list-type="picture-card"
        accept="image/gif,image/jpeg,image/jpg,image/png,image/bmp,image/webp"
      >
        <el-icon><Plus /></el-icon>
      </el-upload>
      <el-dialog v-model="dialogVisible" :append-to-body="true">
        <img :src="urlPic" width="100%" alt="" />
      </el-dialog>
    </div>
    <div
      v-else
      class="picUpload_wrap"
      style="font-size: 14px; line-height: 23px"
    >
      <div class="el-loading-spinner">
        <el-icon class="is-loading" style="font-size: 30px"
          ><Loading
        /></el-icon>
        <p class="el-loading-text">上传中~</p>
      </div>
    </div>
    <!-- 水印图片 -->
  </div>
</template>

<script setup>
import { ref, reactive, computed, defineProps, defineEmits } from "vue";
import { Plus, Loading } from "@element-plus/icons-vue";
import ImageCompressor from "js-image-compressor";
import { fileByBase64, base64ToFile } from "@/utils/utils.js";
import { stsTokenApi } from "@/api/answer.js";

// Props
const props = defineProps({
  urlPic: {
    type: String,
    default: "",
  },
  nameKey: {
    type: String,
    default: "",
  },
  needWater: {
    type: Boolean,
    default: true,
  },
});

// Emits
const emit = defineEmits(["upSuccsessSingle", "handleRemove"]);

// Refs
const uploadRef = ref(null);
const waterImgRef = ref(null);

// Reactive data
const dialogVisible = ref(false);
const dataObj = ref({
  policy: "",
  signature: "",
  key: "",
  ossaccessKeyId: "",
  dir: "",
  host: "",
  success_action_status: "200",
});
const isUploading = ref(false);
const useOss = ref(true);
const ossUploadUrl = ref("https://images2.kkzhw.com");
const minioUploadUrl = ref("http://*************:8201/mall-admin/minio/upload");

// Computed
const imageUrl = computed(() => props.urlPic);

const imageName = computed(() => {
  if (props.urlPic !== "") {
    return props.urlPic.substr(props.urlPic.lastIndexOf("/") + 1);
  } else {
    return null;
  }
});

const fileList = computed(() => {
  return [
    {
      name: imageName.value,
      url: imageUrl.value,
    },
  ];
});

const showFileList = computed(() => {
  return (
    props.urlPic !== null && props.urlPic !== "" && props.urlPic !== undefined
  );
});

// Methods
const handlePreview = () => {
  dialogVisible.value = true;
};

const handleRemove = () => {
  emit("handleRemove");
};

const beforeUpload2 = (file) => {
  return new Promise((resolve, reject) => {
    if (!props.needWater) {
      resolve(file);
    } else {
      fileByBase64(file, async (base64) => {
        let tempCanvas = await imgToCanvas(base64);
        const canvas = addWatermark(tempCanvas, "卖号通");
        const img = convasToImg(canvas);
        let newFile = base64ToFile(img.src, file.name);
        resolve(newFile);
      });
    }
  });
};

const imgToCanvas = async (base64) => {
  const img = document.createElement("img");
  img.setAttribute("src", base64);
  await new Promise((resolve) => (img.onload = resolve));

  const canvas = document.createElement("canvas");
  canvas.width = img.width;
  canvas.height = img.height;
  canvas.getContext("2d").drawImage(img, 0, 0);

  return canvas;
};

const addWatermark = (canvas, text) => {
  return canvas;
};

const convasToImg = (canvas) => {
  let image = new Image();
  image.src = canvas.toDataURL("image/png");
  return image;
};

const handleUploadSuccess = (res, file) => {
  console.log("上传成功回调:", res, file);
  console.log("上传时使用的数据:", dataObj.value);
  isUploading.value = false;

  let url;
  if (!useOss.value) {
    url = res.data?.url;
  } else {
    // 使用完整的 URL 构建
    url = `${dataObj.value.host}/${dataObj.value.key}`;
  }

  console.log("生成的图片URL:", url);
  emit("upSuccsessSingle", url, props.nameKey);
};

const rename = (file, fineName) => {
  console.log("重命名文件:", file.name, "->", fineName);

  const copyFile = new File([file], fineName, {
    type: file.type,
    lastModified: file.lastModified,
  });

  // 保持原有的 uid
  Object.defineProperty(copyFile, "uid", {
    value: file.uid,
    writable: false,
  });

  // 更新 uploadRef 中的文件信息
  if (uploadRef.value?.uploadFiles) {
    const index = uploadRef.value.uploadFiles.findIndex((ele) => {
      return ele.uid === file.uid;
    });

    if (index !== -1) {
      uploadRef.value.uploadFiles[index].raw = copyFile;
      uploadRef.value.uploadFiles[index].name = copyFile.name;
      uploadRef.value.uploadFiles[index].url = URL.createObjectURL(copyFile);
    }
  }

  return copyFile;
};

const beforeUpload = async (file) => {
  console.log("开始上传文件:", file.name);

  try {
    const response = await stsTokenApi();
    console.log("获取STS Token成功:", response);

    const ext = file.name.split(".").pop();
    const fileName = response.data.fileName;

    // 确保所有必要的字段都被设置
    dataObj.value = {
      policy: response.data.policy,
      signature: response.data.signature,
      key: response.data.dir + `/${fileName}.${ext}`,
      ossaccessKeyId: response.data.accessKeyId,
      dir: response.data.dir,
      host: response.data.host,
      success_action_status: "200",
    };

    console.log("设置的上传参数:", dataObj.value);

    const copyFile = rename(file, `${fileName}.${ext}`);

    isUploading.value = true;
    console.log("开始处理文件...");

    let blobO = await beforeUpload2(copyFile);
    let newFile = await compressionImage(blobO);

    console.log("文件处理完成，准备上传");
    return newFile;
  } catch (err) {
    console.error("上传前处理失败:", err);
    isUploading.value = false;
    return false;
  }
};

const compressionImage = (file) => {
  console.log("开始压缩图片:", file.name, file.size);

  return new Promise((resolve, reject) => {
    new ImageCompressor({
      file: file,
      quality: 0.4,
      convertSize: 100000,
      redressOrientation: false,
      beforeCompress: function (result) {
        console.log("压缩之前图片尺寸大小: ", result.size);
      },
      success: function (result) {
        console.log("压缩之后图片尺寸大小: ", result.size);
        let compressedFile = new File([result], result.name, {
          type: result.type,
        });
        resolve(compressedFile);
      },
      error(e) {
        console.error("图片压缩失败:", e);
        reject(e);
      },
    });
  });
};

// 添加上传失败处理
const handleUploadError = (err, file, fileList) => {
  console.error("上传失败:", err);
  console.error("上传时的数据对象:", dataObj.value);
  console.error("文件信息:", file);
  isUploading.value = false;
};

// 添加上传头部信息
const uploadHeaders = computed(() => {
  return {
    "Content-Type": "multipart/form-data",
  };
});

// 添加自定义上传方法
const customUpload = async (options) => {
  const { file } = options;

  try {
    // 构建 FormData
    const formData = new FormData();

    // 添加 OSS 必需的字段
    Object.keys(dataObj.value).forEach((key) => {
      if (dataObj.value[key]) {
        formData.append(key, dataObj.value[key]);
      }
    });

    // 添加文件，注意 file 字段要放在最后
    formData.append("file", file);

    console.log("FormData 内容:");
    for (let [key, value] of formData.entries()) {
      console.log(key, value);
    }

    // 发送请求
    const response = await fetch(
      useOss.value ? ossUploadUrl.value : minioUploadUrl.value,
      {
        method: "POST",
        body: formData,
      }
    );

    if (response.ok) {
      const result = await response.json();
      handleUploadSuccess(result, file);
    } else {
      throw new Error(`上传失败: ${response.status}`);
    }
  } catch (error) {
    console.error("自定义上传失败:", error);
    handleUploadError(error, file);
  }
};
</script>

<style scoped>
.picUpload_wrap .el-upload--picture-card {
  border: 0;
  background: none;
  position: absolute;
  z-index: 4;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
}

.picUpload_btn {
  position: absolute;
  z-index: 4;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  opacity: 0;
  cursor: pointer;
}

.picUpload_pic {
  position: absolute;
  z-index: 1;
  left: 0px;
  right: 0px;
  top: 0px;
  bottom: 0px;
  width: 100%;
  height: 100%;
}

.picUpload_wrap {
  border-radius: 0;
  border: 0;
  position: relative;
  text-align: center;
  line-height: 180px;
  font-size: 28px;
  color: #8c939d;
  overflow: hidden;
}
</style>
