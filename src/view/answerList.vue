<template>
  <div class="headerContent1">
    <img
      @click="goBack"
      src="../assets/cancel.png"
      alt="返回"
      class="back-btn1"
    />
    <span style="color: #000">题库管理</span>
  </div>

  <div class="app-container">
    <!-- 搜索区域 -->
    <!-- <el-card class="filter-container" shadow="never">
      <div style="margin-top: 15px">
        <el-form
          :inline="true"
          :model="listQuery"
          size="default"
          label-width="90px"
        >
          <el-form-item label="题库名称：">
            <el-input
              v-model="listQuery.keyword"
              class="input-width"
              placeholder="请输入题库名称"
              clearable
            />
          </el-form-item>
          
          <el-form-item>
            <el-button
              type="primary"
              size="default"
              @click="handleSearchList()"
            >
              查询搜索
            </el-button>
            <el-button
              style="margin-left: 10px"
              size="default"
              @click="handleResetSearch()"
            >
              重置
            </el-button>
          </el-form-item>
          
          <div style="height: 40px">
            <el-button
              size="mini"
              class="btn-add"
              @click="handleAddCategory"
              style="float: right; margin-right: 16px"
            >
              添加题库
            </el-button>
          </div>
        </el-form>
      </div>
    </el-card> -->
    <div style="height: 40px">
      <el-button
        size="mini"
        class="btn-add"
        @click="handleAddCategory"
        style="float: right; margin-right: 16px"
      >
        添加题库
      </el-button>
      <el-button
        size="mini"
        class="btn-add"
        @click="handleImport"
        style="float: right; margin-right: 16px"
      >
        导入题库
      </el-button>
      <el-button
        size="mini"
        class="btn-add"
        @click="handleExport"
        style="float: right"
      >
        下载模版
      </el-button>
    </div>
    <!-- 表格区域 -->
    <div class="table-container">
      <el-table
        v-loading="listLoading"
        ref="categoryTable"
        :data="list"
        :resizable="false"
        style="width: 100%"
        border
      >
        <el-table-column prop="id" label="ID" width="80" align="center" />
        <el-table-column prop="categoryName" label="题库名称" align="center" />
        <el-table-column label="创建时间" width="180" align="center">
          <template #default="scope">
            <span>{{ formatCreateTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="300" align="center" fixed="right">
          <template #default="scope">
            <el-button
              size="mini"
              type="success"
              @click="goQuestions(scope.row)"
            >
              题目管理
            </el-button>
            <el-button
              size="mini"
              type="primary"
              @click="handleUpdateCategory(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              size="mini"
              type="danger"
              @click="handleDeleteCategory(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <!-- <div class="pagination-container">
      <el-pagination
        :current-page.sync="listQuery.pageNum"
        :page-size="listQuery.pageSize"
        :page-sizes="[20, 40, 60]"
        :total="total"
        background
        layout="total, sizes,prev, pager, next,jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
        <template #total>
          共 {{ total }} 条
        </template>
      </el-pagination>
    </div> -->

    <!-- 添加/编辑题库对话框 -->
    <el-dialog
      :title="isEdit ? '编辑题库' : '添加题库'"
      v-model="dialogVisible"
      width="500px"
    >
      <el-form
        ref="categoryFormRef"
        :model="categoryForm"
        :rules="categoryRules"
        label-width="100px"
      >
        <el-form-item label="题库名称" prop="categoryName">
          <el-input
            v-model="categoryForm.categoryName"
            placeholder="请输入题库名称，如：地下城与勇士、英雄联盟"
          />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          :loading="confirmLoading"
        >
          确定
        </el-button>
      </div>
    </el-dialog>
    <!-- 导入题库对话框 -->
    <el-dialog title="导入题库" v-model="importDialogVisible" width="600px">
      <div class="import-container">
        <div class="import-tips">
          <el-alert title="导入说明" type="info" :closable="false" show-icon>
            <template #default>
              <p>1. 请先下载模版文件，按照模版格式填写题目信息</p>
              <p>2. 支持的文件格式：.xlsx、.xls</p>
              <p>3. 文件大小不超过50MB</p>
              <p>4. 导入的题目将自动归属到当前题库：{{ categoryName }}</p>
            </template>
          </el-alert>
        </div>

        <div class="upload-area">
          <el-upload
            ref="importUploadRef"
            :action="importUrl"
            :data="importData"
            :headers="importHeaders"
            :before-upload="beforeImportUpload"
            :on-success="handleImportSuccess"
            :on-error="handleImportError"
            :on-progress="handleImportProgress"
            :on-change="handleFileChange"
            :file-list="importFileList"
            :limit="1"
            :auto-upload="false"
            accept=".xlsx,.xls"
            drag
          >
            <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
            <div class="el-upload__text">
              将Excel文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                只能上传xlsx/xls文件，且不超过50MB
              </div>
            </template>
          </el-upload>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleConfirmImport"
            :loading="importLoading"
            :disabled="importFileList.length === 0"
          >
            开始导入
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, onUnmounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Delete, UploadFilled } from "@element-plus/icons-vue";
import { useRouter } from "vue-router";
import { invoke } from "@tauri-apps/api/core";
import { exportEx } from "../utils/utils";
import { generateAuthHeaders } from "../utils/utils";
import {
  fetchCategoryList,
  createCategory,
  updateCategory,
  deleteCategory,
} from "@/api/answer";

const router = useRouter();

function goBack() {
  router.push({ path: "/" });
}

// 跳转到题目管理页面
function goQuestions(row) {
  router.push({
    path: "/questions",
    query: {
      categoryName: row.categoryName,
      categoryId: row.id,
    },
  });
}

const defaultListQuery = {
  //   pageNum: 1,
  //   pageSize: 20,
  //   keyword: "",
};

const defaultCategoryForm = {
  categoryName: "",
};

// 响应式数据
const listLoading = ref(false);
const confirmLoading = ref(false);
const list = ref([]);
const total = ref(0);
const listQuery = reactive({ ...defaultListQuery });
const dialogVisible = ref(false);
const isEdit = ref(false);
const categoryForm = ref({ ...defaultCategoryForm });

// 表单引用
const categoryFormRef = ref(null);

// 表单验证规则
const categoryRules = {
  categoryName: [
    { required: true, message: "请输入题库名称", trigger: "blur" },
    {
      min: 1,
      max: 50,
      message: "题库名称长度在1到50个字符",
      trigger: "blur",
    },
  ],
};

const handleExport = () => {
  exportEx("http://api2.kkzhw.com/mall-portal/openapi/quiz/template/download");
};

// 格式化时间过滤器函数
const formatCreateTime = (time) => {
  if (!time) return "";
  const date = new Date(time);
  return date.toLocaleString("zh-CN");
};

// 获取题库列表
const getList = async () => {
  listLoading.value = true;
  try {
    const response = await fetchCategoryList(listQuery);
    console.log(response, "题库列表");

    if (response.code === 200) {
      list.value = response.data || [];
      //   total.value = response.data.total || 0;
    }
  } catch (error) {
    console.error("获取题库列表失败", error);
    ElMessage.error("获取题库列表失败");
  } finally {
    listLoading.value = false;
  }
};

// 搜索
const handleSearchList = () => {
  listQuery.pageNum = 1;
  getList();
};

// 重置搜索
const handleResetSearch = () => {
  Object.assign(listQuery, defaultListQuery);
  getList();
};

// 分页大小改变
const handleSizeChange = (val) => {
  listQuery.pageSize = val;
  listQuery.pageNum = 1;
  getList();
};

// 当前页改变
const handleCurrentChange = (val) => {
  listQuery.pageNum = val;
  getList();
};

// 添加题库
const handleAddCategory = () => {
  isEdit.value = false;
  categoryForm.value = { ...defaultCategoryForm };
  dialogVisible.value = true;
  nextTick(() => {
    categoryFormRef.value?.clearValidate();
  });
};

// 编辑题库
const handleUpdateCategory = (row) => {
  isEdit.value = true;
  categoryForm.value = { ...row };
  dialogVisible.value = true;
  nextTick(() => {
    categoryFormRef.value?.clearValidate();
  });
};

// 删除题库
const handleDeleteCategory = async (row) => {
  try {
    await ElMessageBox.confirm(
      "确认要删除该题库吗？删除后该题库下的所有题目也将被删除！",
      "提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
    );

    await deleteCategory(row.id);
    ElMessage.success("删除成功");
    getList();
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除失败", error);
      ElMessage.error("删除失败");
    }
  }
};

// 确认添加/编辑
const handleConfirm = () => {
  categoryFormRef.value?.validate(async (valid) => {
    if (valid) {
      confirmLoading.value = true;

      try {
        if (isEdit.value) {
          await updateCategory(categoryForm.value.id, categoryForm.value);
          ElMessage.success("编辑成功");
        } else {
          await createCategory(categoryForm.value);
          ElMessage.success("添加成功");
        }
        dialogVisible.value = false;
        getList();
      } catch (error) {
        console.error("操作失败", error);
        ElMessage.error("操作失败");
      } finally {
        confirmLoading.value = false;
      }
    }
  });
};

// 添加导入相关的响应式数据
const importDialogVisible = ref(false);
const importLoading = ref(false);
const importFileList = ref([]);
const importUploadRef = ref(null);

// 导入配置
const importUrl = ref(
  "http://api2.kkzhw.com/mall-portal/openapi/quiz/import/excel"
);
const importData = ref({});
const importHeaders = ref({});

// 打开导入对话框
const handleImport = () => {
  importDialogVisible.value = true;
  importFileList.value = [];
  nextTick(() => {
    importUploadRef.value?.clearFiles();
  });
};

// 文件选择改变事件
const handleFileChange = (file, fileList) => {
  console.log("文件选择改变:", file, fileList);
  importFileList.value = fileList;
};

// 导入前的文件检查
const beforeImportUpload = (file) => {
  console.log("准备导入文件:", file);

  // 检查文件类型
  const isExcel =
    file.type ===
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
    file.type === "application/vnd.ms-excel" ||
    file.name.endsWith(".xlsx") ||
    file.name.endsWith(".xls");

  if (!isExcel) {
    ElMessage.error("只能上传Excel文件！");
    return false;
  }

  // 检查文件大小
  const isLt10M = file.size / 1024 / 1024 < 50;
  if (!isLt10M) {
    ElMessage.error("文件大小不能超过50MB！");
    return false;
  }

  return true;
};

// 确认导入
const handleConfirmImport = async () => {
  if (importFileList.value.length === 0) {
    ElMessage.warning("请先选择要导入的Excel文件");
    return;
  }

  try {
    importLoading.value = true;

    // 设置导入参数
    // importData.value = {
    //   gameCategory: categoryName.value,
    // };

    // 设置请求头
    importHeaders.value = await generateAuthHeaders();

    // 开始上传
    importUploadRef.value?.submit();
  } catch (error) {
    console.error("导入失败:", error);
    ElMessage.error("导入失败，请重试");
    importLoading.value = false;
  }
};

// 导入进度
const handleImportProgress = (event) => {
  console.log("导入进度:", event.percent);
};

// 导入成功
const handleImportSuccess = (response) => {
  console.log("导入成功:", response);
  importLoading.value = false;

  if (response.code === 200) {
    ElMessage.success(
      `导入成功！共导入 ${response.data?.successCount || 0} 条题目`
    );
    importDialogVisible.value = false;
    importFileList.value = [];
    // 刷新题目列表
    getList();
  } else {
    ElMessage.error(response.message || "导入失败");
  }
};

// 导入失败
const handleImportError = (error) => {
  console.error("导入失败:", error);
  importLoading.value = false;
  ElMessage.error("导入失败，请检查文件格式是否正确");
};

// 组件挂载时执行
onMounted(async () => {
  getList();
  await invoke("set_window_width", { width: 1000 });
});
onUnmounted(async () => {
  await invoke("set_window_width", { width: 500 });
});
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.table-container {
  margin-bottom: 20px;
}

.pagination-container {
  text-align: center;
  padding: 20px 0;
}

.input-width {
  width: 200px;
}

.dialog-footer {
  text-align: right;
}

.el-form-item {
  margin-right: 10px;
}

.headerContent1 {
  display: flex;
  align-items: center;
  padding: 20px 20px 10px 20px;
  font-size: 20px;
  font-weight: bold;
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.back-btn1 {
  width: 20px;
  height: 20px;
  margin-right: 15px;
  cursor: pointer;
}
.import-container {
  padding: 20px 0;
}

.import-tips {
  margin-bottom: 20px;
}

.import-tips .el-alert p {
  margin: 5px 0;
  line-height: 1.5;
}

.upload-area {
  margin-top: 20px;
}

.upload-area .el-upload-dragger {
  width: 100%;
  height: 180px;
}

.dialog-footer {
  text-align: right;
}
</style>
