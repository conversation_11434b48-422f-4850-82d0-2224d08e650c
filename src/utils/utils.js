import axios from 'axios'
export function generateAuthHeaders() {
  const skdToken1 = localStorage.getItem("skdToken1") || "";
  const deviceCode1 = localStorage.getItem("deviceCode1") || "7788AABBCC11";
  const timestamp = Math.floor(Date.now() / 1000).toString();
  const secretKey = "chbtymhosqvxhwssanoo"; // 从配置或环境变量获取

  // 使用 HMAC-SHA256 生成签名
  function generateSignature(skdToken1, deviceCode1, timestamp, secretKey) {
    // 1. 拼接签名内容：CDKey + 设备码 + 时间戳
    const content = skdToken1 + deviceCode1 + timestamp;

    // 2. 创建 HMAC-SHA256 签名
    const encoder = new TextEncoder();
    const contentBuffer = encoder.encode(content);
    const keyBuffer = encoder.encode(secretKey);

    // 3. 使用 Web Crypto API 进行 HMAC 签名（异步）
    return window.crypto.subtle
      .importKey("raw", keyBuffer, { name: "HM<PERSON>", hash: "SHA-256" }, false, [
        "sign",
      ])
      .then((key) => {
        return window.crypto.subtle.sign("HMAC", key, contentBuffer);
      })
      .then((signatureBuffer) => {
        // 4. 将 ArrayBuffer 转换为 Base64
        const signatureArray = new Uint8Array(signatureBuffer);
        const base64 = btoa(String.fromCharCode(...signatureArray));

        // 5. 转换为 URL-safe 格式
        return base64
          .replace(/\+/g, "-")
          .replace(/\//g, "_")
          .replace(/=+$/, "");
      });
  }

  // 返回 Promise
  return generateSignature(skdToken1, deviceCode1, timestamp, secretKey).then(
    (signature) => {
      return {
        // "X-Token": skdToken1,
        "X-Device": deviceCode1,
        "X-Timestamp": timestamp,
        "X-Signature": signature,
      };
    }
  );
}

/**
 * 文件转base64
 * @param  file 文件流
 * @param  callback 回调函数
 */
export function fileByBase64(file, callback) {
    const reader = new FileReader();
    // 传入一个参数对象即可得到基于该参数对象的文本内容
    reader.readAsDataURL(file);
    reader.onload = function (e) {
      // target.result 该属性表示目标对象的DataURL
      callback(e.target.result);
    };
  }
  
  /**
   *
   * @param urlData  base64
   * @param fileName 文件名称
   * @returns {File}
   */
  export function base64ToFile(urlData, fileName) {
    const arr = urlData.split(',');
    const mime = arr[0].match(/:(.*?);/)[1];
    const bytes = atob(arr[1]); // 解码base64
    let n = bytes.length;
    const ia = new Uint8Array(n);
    while (n--) {
      ia[n] = bytes.charCodeAt(n);
    }
    return new File([ia], fileName, { type: mime });
  }

  export function exportEx (url, query) {
    axios({
      method: 'get',
      url: url,
      responseType: 'blob',
    })
      .then((response) => {
        const url = window.URL.createObjectURL(new Blob([response.data]));
  
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', 'filename.xlsx'); // 替换为你的文件名
        document.body.appendChild(link);
        link.click();
        // 清理工作
        window.URL.revokeObjectURL(url);
        document.body.removeChild(link);
      })
      .catch((error) => {
        console.error('There was an error downloading the file:', error);
      });
  };