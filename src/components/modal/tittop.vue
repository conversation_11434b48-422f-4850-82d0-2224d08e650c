<template>
  <div class="modalBox">
    <div
      class="imgCircle"
      :style="{ borderColor: type === 'success' ? '#6dc032' : '#ff4d4f' }"
    >
      <img :src="imgSrc" alt="" />
    </div>
    <div class="resultText" :style="{ color: textColor }">{{ text }}</div>
  </div>
</template>

<script setup>
import { defineProps, computed } from 'vue'

const props = defineProps({
  type: {
    type: String,
    default: 'success'
  }
})

import successImg from '../../assets/success.png'
import errorImg from '../../assets/error.png'
const imgSrc = computed(() => {
  return props.type === 'success' ? successImg : errorImg
})

const text = computed(() => {
  return props.type === 'success' ? '回答正确' : '回答错误'
})

const textColor = computed(() => {
  return props.type === 'success' ? '#6dc032' : '#ff4d4f'
})

const type = props.type
</script>

<style scoped>
.modalBox {
  width: 200px;
  height: 200px;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%,-50%);
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  z-index: 9999;
}
.imgCircle {
  background: #fff;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}
.imgCircle img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
.resultText {
  font-size: 20px;
  margin-top: 16px;
  font-weight: bold;
  letter-spacing: 2px;
}
</style>