<template>
  <div v-if="isCdkFlag" class="cdkContentModal">
    <div v-if="isCdkNullFlag" class="tittopModalText">请输入CDK</div>
    <div class="cdkModalBox">
      <div class="cdkTitle">请输入CDK激活</div>
      <input
        v-model="cdkInput"
        class="cdkInput"
        placeholder="请输入CDK"
        type="text"
      />
      <div style="display: flex; margin-top: 30px">
        <!-- <div @click="cancelBtn" class="modalBtn1 modalCancel1 btnStyleAm2">取消</div> -->
        <div @click="submit" class="modalBtn1 modalSubmit1 btnStyleAm2">
          确定
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, onMounted } from "vue";
let cdkInput = ref("");
let isCdkNullFlag = ref(false);
import { defineEmits } from "vue";
const emit = defineEmits(["success"]);

const props = defineProps({
  isCdkFlag: {
    type: Boolean,
    default: false
  }
});


async function submit() {
  if (!cdkInput.value || cdkInput.value.trim() === "") {
    isCdkNullFlag.value = true;
    setTimeout(() => {
      isCdkNullFlag.value = false;
    }, 1000);
    return;
  }
  localStorage.setItem("skdToken1", cdkInput.value.trim());
  // 传递出去成功的方法
  emit("successBtn", cdkInput.value.trim());
}
</script>
<style scoped>
.cdkContentModal {
  position: fixed;
  top: 0px;
  left: 0px;
  height: 100vh;
  width: 100%;
  background: rgba(0, 0, 0, 0.5);
}
.cdkModalBox {
  background: #fff;
  width: 300px;
  height: 180px;
  border-radius: 20px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  padding: 20px;
  flex-direction: column;
}
.cdkTitle {
  font-size: 18px;
}
.cdkInput {
  width: calc(100% - 20px);
  height: 40px;
  border-radius: 30px;
  border: 3px solid transparent;
  background-image: linear-gradient(#fff, #fff),
    linear-gradient(60deg, #ff741c 0%, #f5a131 100%);
  background-origin: border-box;
  background-clip: padding-box, border-box;
  margin-top: 30px;
  padding-left: 15px;
  padding-right: 15px;
  box-sizing: border-box;
  transition: border-color 0.2s;
}
.cdkInput:focus {
  outline: none;
  border: 3px solid #f5a131;
}
.modalBtn1 {
  width: 180px;
  height: 46px;
  border-radius: 46px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  margin-left: 10px;

  cursor: pointer;
}
.modalCancel1 {
  background: linear-gradient(10deg, #444 0%, #ccc 100%);
}
.modalSubmit1 {
  background: linear-gradient(60deg, #f65c02 0%, #f5a131 100%);
}
.btnStyleAm2 {
  color: white;
  font-size: 20px; /* 字体增大 */
  font-weight: bold;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;

  /* 增强3D透视效果 */
  transform: perspective(1500px) rotateX(8deg); /* 增加透视深度和旋转角度 */
  transform-style: preserve-3d;

  box-shadow: 0 1px 4px -8px rgba(0, 0, 0, 0.4),
    /* 主阴影加深，扩散更远 */ 0 1px 2px -1px rgba(0, 0, 0, 0.25),
    /* 中层阴影 */ 0 8px 12px -3px rgba(0, 0, 0, 0.2),
    /* 近层阴影 */ inset 0 0 0 1.5px rgba(255, 255, 255, 0.3),
    /* 边缘高光加粗 */ inset 0 10px 10px 0 rgba(255, 255, 255, 0.5),
    /* 顶部强高光 */ inset 0 -18px 25px 0 rgba(0, 0, 0, 0.2);
}
/* 增强镜面效果 - 扩大高光区域 */
.btnStyleAm2::before {
  content: "";
  position: absolute;
  top: 10px;
  left: 14px;
  width: 15px;
  height: 10px;
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(255, 255, 255, 0.1) 100%
  );
  transform: skewY(-15deg);
  border-radius: 32px;
}

/* 立体边框效果 - 增强边缘厚度和对比度 */
.btnStyleAm2::after {
  content: "";
  position: absolute;
  top: 1.8px;
  left: 1.8px;
  right: 1.8px;
  bottom: 1.8px;
  border-radius: 31px;
  border-top: 2px solid rgba(255, 255, 255, 0.6); /* 顶部边框更亮更粗 */
  border-left: 2px solid rgba(255, 255, 255, 0.5); /* 左侧边框增强 */
  border-right: 2px solid rgba(0, 0, 0, 0.2); /* 右侧边框加深 */
  border-bottom: 2px solid rgba(0, 0, 0, 0.3); /* 底部边框加深 */
  pointer-events: none;
}

/* 按钮文字 */
.btnStyleAm2 span {
  position: relative;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
}
/* 悬停效果 - 增强变化幅度 */
.btnStyleAm2:hover {
  /* transform: perspective(1500px) rotateX(6deg) translateY(-5px); */
  box-shadow: 0 3px 5px -10px rgba(0, 0, 0, 0.45),
    0 20px 25px -8px rgba(0, 0, 0, 0.3), 0 10px 15px -5px rgba(0, 0, 0, 0.2),
    inset 0 0 0 2px rgba(255, 255, 255, 0.4),
    inset 0 10px 15px 0 rgba(255, 255, 255, 0.6),
    inset 0 -22px 30px 0 rgba(0, 0, 0, 0.15);
}

/* 点击效果 */
.btnStyleAm2:active {
  transform: perspective(1500px) rotateX(8deg) translateY(-4px);
  box-shadow: 0 18px 30px -8px rgba(0, 0, 0, 0.35),
    0 10px 15px -5px rgba(0, 0, 0, 0.2), 0 5px 8px -3px rgba(0, 0, 0, 0.15),
    inset 0 0 0 1.5px rgba(255, 255, 255, 0.3),
    inset 0 12px 18px 0 rgba(255, 255, 255, 0.5),
    inset 0 -12px 18px 0 rgba(0, 0, 0, 0.2);
}
.tittopModalText {
  position: absolute;
  z-index: 9999;
  max-width: 80%;
  padding: 10px 20px;
  height: 20px;
  background: rgba(0, 0, 0, 0.6);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #fff;
  border-radius: 10px;
}
</style>
