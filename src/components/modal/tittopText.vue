<template>
  <div v-if="isTittopTextFlag" class="tittopModalText">{{ text }}</div>
</template>

<script setup>
import { ref, onMounted } from "vue";
let isTittopTextFlag = ref(false);
let text=ref("")
function open(msg){
    text.value = msg;
    isTittopTextFlag.value = true;
    setTimeout(() => {
      isTittopTextFlag.value = false;
    }, 1000);
}
defineExpose({ open });
</script>

<style scoped>
.tittopModalText {
  position: absolute;
  z-index: 9999;
  max-width: 80%;
  padding: 10px 20px;
  height: auto;
  background: rgba(0, 0, 0, 0.6);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #fff;
  border-radius: 10px;
}
</style>
