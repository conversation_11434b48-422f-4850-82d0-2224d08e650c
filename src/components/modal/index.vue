<template>
    <div class="modalBox">
        <div class="modalContent">
            <div class="modalTitle">提示</div>
            <div class="modalContentText">
                确定要退出答题吗？
            </div>
            <div class="modalFooter">
                <div @click="cancelBtn" class="modalBtn modalCancel btnStyleAm1">取消</div>
                <div @click="submit" class="modalBtn modalSubmit btnStyleAm1">确定</div>
            </div>
        </div>
    </div>
</template>
<script setup>
    import { defineEmits } from 'vue'
    const emit = defineEmits(['cancel', 'submit'])

    function cancelBtn() {
        emit('cancel')
    }
    function submit() {
        emit('submit')
    }
</script>
<style scoped>
    .modalBox{
        width: 100%;
        height: 100vh;
        position: fixed;
        top: 0px;
        left: 0px;
        background: rgba(0, 0, 0, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .modalContent{
        width: 350px;
        height: 250px;
        background: #fff;
        border-radius: 20px;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20px;
        box-sizing: border-box;
    }
    .modalTitle{
        font-size: 18px;
        font-weight: 1000;
        color: #2d8cf0;
    }
    .modalContentText{
        margin-top: 50px;
    }
    .modalFooter{
        display: flex;
        align-items: center;
        margin-top: 60px;
    }
    .modalBtn{
        width: 120px;
        height: 46px;
        border-radius: 46px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 10px;
        margin-left: 10px;
        
        cursor: pointer;
    }
    .modalCancel{
       background: linear-gradient(10deg, #444 0%, #ccc 100%)
    }
    .modalSubmit{
        background: linear-gradient(60deg, #f65c02 0%, #f5a131 100%);
    }
    .btnStyleAm1 {
  color: white;
  font-size: 20px; /* 字体增大 */
  font-weight: bold;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;

  /* 增强3D透视效果 */
  transform: perspective(1500px) rotateX(8deg); /* 增加透视深度和旋转角度 */
  transform-style: preserve-3d;

  box-shadow: 0 1px 4px -8px rgba(0, 0, 0, 0.4),
    /* 主阴影加深，扩散更远 */ 0 1px 2px -1px rgba(0, 0, 0, 0.25),
    /* 中层阴影 */ 0 8px 12px -3px rgba(0, 0, 0, 0.2),
    /* 近层阴影 */ inset 0 0 0 1.5px rgba(255, 255, 255, 0.3),
    /* 边缘高光加粗 */ inset 0 10px 10px 0 rgba(255, 255, 255, 0.5),
    /* 顶部强高光 */ inset 0 -18px 25px 0 rgba(0, 0, 0, 0.2);
}
/* 增强镜面效果 - 扩大高光区域 */
.btnStyleAm1::before {
  content: "";
  position: absolute;
  top: 10px;
  left: 14px;
  width: 15px;
  height: 10px;
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(255, 255, 255, 0.1) 100%
  );
  transform: skewY(-15deg);
  border-radius: 32px;
}

/* 立体边框效果 - 增强边缘厚度和对比度 */
.btnStyleAm1::after {
  content: "";
  position: absolute;
  top: 1.8px;
  left: 1.8px;
  right: 1.8px;
  bottom: 1.8px;
  border-radius: 31px;
  border-top: 2px solid rgba(255, 255, 255, 0.6); /* 顶部边框更亮更粗 */
  border-left: 2px solid rgba(255, 255, 255, 0.5); /* 左侧边框增强 */
  border-right: 2px solid rgba(0, 0, 0, 0.2); /* 右侧边框加深 */
  border-bottom: 2px solid rgba(0, 0, 0, 0.3); /* 底部边框加深 */
  pointer-events: none;
}

/* 按钮文字 */
.btnStyleAm1 span {
  position: relative;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
}
/* 悬停效果 - 增强变化幅度 */
.btnStyleAm1:hover {
  transform: perspective(1500px) rotateX(6deg) translateY(-5px);
  box-shadow: 0 3px 5px -10px rgba(0, 0, 0, 0.45),
    0 20px 25px -8px rgba(0, 0, 0, 0.3), 0 10px 15px -5px rgba(0, 0, 0, 0.2),
    inset 0 0 0 2px rgba(255, 255, 255, 0.4),
    inset 0 10px 15px 0 rgba(255, 255, 255, 0.6),
    inset 0 -22px 30px 0 rgba(0, 0, 0, 0.15);
}

/* 点击效果 */
.btnStyleAm1:active {
  transform: perspective(1500px) rotateX(8deg) translateY(-4px);
  box-shadow: 0 18px 30px -8px rgba(0, 0, 0, 0.35),
    0 10px 15px -5px rgba(0, 0, 0, 0.2), 0 5px 8px -3px rgba(0, 0, 0, 0.15),
    inset 0 0 0 1.5px rgba(255, 255, 255, 0.3),
    inset 0 12px 18px 0 rgba(255, 255, 255, 0.5),
    inset 0 -12px 18px 0 rgba(0, 0, 0, 0.2);
}
</style>