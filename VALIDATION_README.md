# 题目数据验证功能

## 功能概述

本功能为Excel题目导入系统添加了严格的数据验证机制，确保导入的题目数据完整性和规范性。

## 验证规则

### 必填字段验证

以下字段不能为空：

1. **题目问题** - 题目的具体内容
2. **题目难度** - 题目的难度等级
3. **所属题库** - 题目归属的题库名称
4. **题目类型** - 题目的类型（由工作表名称确定）
5. **正确答案** - 题目的标准答案

### 选择题特殊验证

对于选择题类型，还需要满足：

1. **选项不能为空** - 必须提供选项内容
2. **至少2个有效选项** - 选择题必须有至少2个有效的选项

## 错误处理机制

### 验证失败时的行为

1. **停止解析** - 发现验证错误时，停止继续处理数据
2. **显示错误表格** - 以表格形式展示所有不合规的数据
3. **详细错误信息** - 每行数据的具体错误原因

### 错误表格包含信息

- **行号** - Excel中的具体行号（包含表头偏移）
- **工作表** - 出错数据所在的工作表名称
- **题目问题** - 题目内容（如果为空显示"(空)"）
- **错误信息** - 具体的验证失败原因

## 使用方法

### 1. 准备Excel文件

- 工作表名称设置为题目类型（如"选择题"、"判断题"等）
- 按照规定格式填写数据

### 2. 上传和验证

- 选择Excel文件
- 点击"解析Excel"按钮
- 系统自动进行数据验证

### 3. 处理验证错误

- 如果有验证错误，会显示红色的错误表格
- 根据错误信息修正Excel文件中的数据
- 重新上传修正后的文件

### 4. 成功导入

- 所有数据验证通过后，显示正常的数据表格
- 数据可以正常提交到后端系统

## 示例

### 正确的数据格式

| 问题 | 题目难度 | 题库名称 | 正确答案 | 选项 |
|------|----------|----------|----------|------|
| 1+1等于几？ | 简单 | 数学题库 | 2 | A: 1<br>B: 2<br>C: 3<br>D: 4 |

### 常见错误示例

| 错误类型 | 示例 | 错误信息 |
|----------|------|----------|
| 题目问题为空 | (空) | 题目问题不能为空 |
| 题目难度为空 | 问题有内容但难度为空 | 题目难度不能为空 |
| 所属题库为空 | 问题和难度有内容但题库为空 | 所属题库不能为空 |
| 正确答案为空 | 其他字段有内容但答案为空 | 正确答案不能为空 |
| 选择题选项为空 | 选择题但选项字段为空 | 选择题的选项不能为空 |
| 选择题选项不足 | 选择题但只有1个选项 | 选择题至少需要2个有效选项 |

## 技术实现

### 验证函数

- `validateQuestionData()` - 主要验证函数
- `generateBackendJson()` - 集成验证的数据生成函数

### 错误收集

- 收集所有验证错误
- 提供详细的错误位置和原因
- 支持批量错误显示

### 用户界面

- 错误表格使用Element Plus组件
- 红色主题突出错误信息
- 清晰的错误分类和描述

## 注意事项

1. **行号计算** - 错误表格中的行号已包含表头偏移，直接对应Excel中的行号
2. **工作表名称** - 工作表名称直接作为题目类型，请确保命名规范
3. **选项格式** - 选择题选项支持A:、B:等前缀格式，系统会自动清理格式
4. **数据清理** - 系统会自动清理选项中的【答案】标记等无关内容

## 更新日志

- 添加完整的数据验证机制
- 实现错误表格显示功能
- 支持选择题特殊验证规则
- 优化用户体验和错误提示
