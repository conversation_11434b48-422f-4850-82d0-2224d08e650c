<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>题目验证测试</title>
</head>
<body>
    <h1>题目数据验证功能测试</h1>
    
    <h2>功能说明：</h2>
    <ul>
        <li>验证题目问题不能为空</li>
        <li>验证题目难度不能为空</li>
        <li>验证所属题库不能为空</li>
        <li>验证题目类型不能为空</li>
        <li>验证正确答案不能为空</li>
        <li>如果是选择题，选项也不能为空，且至少需要2个有效选项</li>
    </ul>
    
    <h2>测试数据示例：</h2>
    
    <h3>正确的数据格式：</h3>
    <table border="1" style="border-collapse: collapse; margin: 10px 0;">
        <tr>
            <th>问题</th>
            <th>题目难度</th>
            <th>题库名称</th>
            <th>正确答案</th>
            <th>选项</th>
        </tr>
        <tr>
            <td>1+1等于几？</td>
            <td>简单</td>
            <td>数学题库</td>
            <td>2</td>
            <td>A: 1<br>B: 2<br>C: 3<br>D: 4</td>
        </tr>
    </table>
    
    <h3>错误的数据格式（会被验证拦截）：</h3>
    <table border="1" style="border-collapse: collapse; margin: 10px 0;">
        <tr>
            <th>问题</th>
            <th>题目难度</th>
            <th>题库名称</th>
            <th>正确答案</th>
            <th>选项</th>
            <th>预期错误</th>
        </tr>
        <tr>
            <td></td>
            <td>简单</td>
            <td>数学题库</td>
            <td>2</td>
            <td>A: 1<br>B: 2</td>
            <td>题目问题不能为空</td>
        </tr>
        <tr>
            <td>2+2等于几？</td>
            <td></td>
            <td>数学题库</td>
            <td>4</td>
            <td>A: 3<br>B: 4</td>
            <td>题目难度不能为空</td>
        </tr>
        <tr>
            <td>3+3等于几？</td>
            <td>简单</td>
            <td></td>
            <td>6</td>
            <td>A: 5<br>B: 6</td>
            <td>所属题库不能为空</td>
        </tr>
        <tr>
            <td>4+4等于几？</td>
            <td>简单</td>
            <td>数学题库</td>
            <td></td>
            <td>A: 7<br>B: 8</td>
            <td>正确答案不能为空</td>
        </tr>
        <tr>
            <td>5+5等于几？</td>
            <td>简单</td>
            <td>数学题库</td>
            <td>10</td>
            <td></td>
            <td>选择题的选项不能为空</td>
        </tr>
        <tr>
            <td>6+6等于几？</td>
            <td>简单</td>
            <td>数学题库</td>
            <td>12</td>
            <td>A: 12</td>
            <td>选择题至少需要2个有效选项</td>
        </tr>
    </table>
    
    <h2>使用方法：</h2>
    <ol>
        <li>准备Excel文件，工作表名称为题目类型（如"选择题"、"判断题"等）</li>
        <li>在Excel中按照上述格式填写数据</li>
        <li>上传Excel文件进行解析</li>
        <li>如果有验证错误，会显示红色的错误表格，列出所有不合规的数据</li>
        <li>修正错误后重新上传</li>
    </ol>
</body>
</html>
